import { useTheme } from "@/contexts/theme-context";
import { NavBar } from "./components/layout/nav-bar";

export const App = () => {
  const { actualTheme } = useTheme();

  return (
    <main className="min-h-screen w-full relative bg-background transition-colors duration-300">
      {/* Background gradient that adapts to theme */}
      <section
        className="absolute inset-0 z-0"
        style={{
          background:
            actualTheme === "dark"
              ? "radial-gradient(ellipse 80% 60% at 50% 0%, rgba(139, 92, 246, 0.25), transparent 70%)"
              : "radial-gradient(circle at top center, rgba(173, 109, 244, 0.3), transparent 70%)",
          filter: actualTheme === "light" ? "blur(80px)" : "none",
        }}
      />

      {/* Content */}
      <section className="relative z-10">
        <NavBar />
      </section>
    </main>
  );
};
