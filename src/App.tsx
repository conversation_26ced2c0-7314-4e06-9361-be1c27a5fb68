import { useTheme } from "@/contexts/theme-context";
import { DesktopNavBar } from "./components/layout/desktop-nav-bar";
import { Footer } from "./components/layout/footer";
import { MobileNavBar } from "./components/layout/mobile-nav-bar";
import { ScrollButton } from "./components/scroll-button";
import { HomeSection } from "./components/sections/home-section";
import { AboutSection } from "./components/sections/about-section";
import { ExperienceSection } from "./components/sections/experience-section";
import { SkillSection } from "./components/sections/skill-section";
import { EducationSection } from "./components/sections/education-section";
import { ContactSection } from "./components/sections/contact-section";

export const App = () => {
  const { actualTheme } = useTheme();

  return (
    <main className="min-h-screen w-full relative bg-background transition-colors duration-300">
      <section
        className="absolute inset-0 z-0"
        style={{
          background:
            actualTheme === "dark"
              ? "radial-gradient(ellipse 80% 60% at 50% 0%, rgba(139, 92, 246, 0.25), transparent 70%)"
              : "radial-gradient(circle at top center, rgba(173, 109, 244, 0.3), transparent 70%)",
          filter: actualTheme === "light" ? "blur(80px)" : "none",
        }}
      />

      {/* Content */}
      <div className="relative z-10">
        <DesktopNavBar />
        <MobileNavBar />
        <ScrollButton />

        <HomeSection />
        <AboutSection />
        <EducationSection />
        <SkillSection />
        <ExperienceSection />
        <ContactSection />
        <Footer />
      </div>
    </main>
  );
};
