import { useTheme } from "@/contexts/theme-context";
import { DesktopNavBar } from "./components/layout/desktop-nav-bar";
import { Footer } from "./components/layout/footer";
import { MobileNavBar } from "./components/layout/mobile-nav-bar";
import { ScrollButton } from "./components/scroll-button";

export const App = () => {
  const { actualTheme } = useTheme();

  return (
    <main className="min-h-screen w-full relative bg-background transition-colors duration-300">
      <section
        className="absolute inset-0 z-0"
        style={{
          background:
            actualTheme === "dark"
              ? "radial-gradient(ellipse 80% 60% at 50% 0%, rgba(139, 92, 246, 0.25), transparent 70%)"
              : "radial-gradient(circle at top center, rgba(173, 109, 244, 0.3), transparent 70%)",
          filter: actualTheme === "light" ? "blur(80px)" : "none",
        }}
      />

      {/* Content */}
      <div className="relative z-10">
        <DesktopNavBar />
        <MobileNavBar />
        <ScrollButton />

        {/* Home Section */}
        <section
          id="home"
          className="min-h-screen flex items-center justify-center px-6"
        >
          <div className="text-center">
            <h1 className="text-5xl md:text-7xl font-bold text-foreground mb-6">
              Hi, I'm <span className="text-primary">Hazel</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              A passionate developer creating beautiful and functional web
              experiences
            </p>
          </div>
        </section>

        {/* About Section */}
        <section
          id="about"
          className="min-h-screen flex items-center justify-center px-6"
        >
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl font-bold text-foreground mb-8">
              About Me
            </h2>
            <p className="text-lg text-muted-foreground leading-relaxed">
              I'm a full-stack developer with a passion for creating innovative
              solutions. With expertise in modern web technologies, I love
              turning ideas into reality through clean, efficient code and
              thoughtful design.
            </p>
          </div>
        </section>

        {/* Projects Section */}
        <section
          id="projects"
          className="min-h-screen flex items-center justify-center px-6"
        >
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl font-bold text-foreground mb-8">
              Projects
            </h2>
            <p className="text-lg text-muted-foreground leading-relaxed">
              Here are some of the projects I've worked on. Each one represents
              a unique challenge and learning opportunity that has helped me
              grow as a developer.
            </p>
          </div>
        </section>

        {/* Contact Section */}
        <section
          id="contact"
          className="min-h-screen flex items-center justify-center px-6 "
        >
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl font-bold text-foreground mb-8">
              Get In Touch
            </h2>
            <p className="text-lg text-muted-foreground leading-relaxed mb-8">
              I'm always open to discussing new opportunities and interesting
              projects. Let's connect and see how we can work together!
            </p>
            <button className="bg-primary text-primary-foreground px-8 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors">
              Contact Me
            </button>
          </div>
        </section>
        <Footer />
      </div>
    </main>
  );
};
