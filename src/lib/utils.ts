import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const handleClickToSection = (href: string) => {
  const elementId = href;
  const element = document.getElementById(elementId);

  if (element) {
    const navbarHeight = 5;
    const extraOffset = 5;
    const totalOffset = navbarHeight + extraOffset;

    const elementPosition =
      element.getBoundingClientRect().top + window.pageYOffset;
    const offsetPosition = elementPosition - totalOffset;

    window.scrollTo({
      top: offsetPosition,
      behavior: "smooth",
    });
  }
};
