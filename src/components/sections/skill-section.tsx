import { motion } from "motion/react";
import {
  BarChart3,
  Users,
  Target,
  TrendingUp,
  MessageSquare,
  PieChart,
  Settings,
  Globe,
  Briefcase,
} from "lucide-react";

const skillCategories = [
  {
    title: "Strategic Planning",
    icon: Target,
    skills: [
      { name: "Business Strategy", level: 95 },
      { name: "Market Analysis", level: 90 },
      { name: "Competitive Intelligence", level: 88 },
      { name: "Strategic Planning", level: 92 },
    ],
  },
  {
    title: "Analytics & Data",
    icon: BarChart3,
    skills: [
      { name: "Financial Analysis", level: 90 },
      { name: "Data Analytics", level: 85 },
      { name: "Performance Metrics", level: 88 },
      { name: "Business Intelligence", level: 82 },
    ],
  },
  {
    title: "Leadership & Management",
    icon: Users,
    skills: [
      { name: "Team Leadership", level: 93 },
      { name: "Project Management", level: 90 },
      { name: "Change Management", level: 87 },
      { name: "Stakeholder Management", level: 89 },
    ],
  },
  {
    title: "Business Development",
    icon: TrendingUp,
    skills: [
      { name: "Growth Strategy", level: 91 },
      { name: "Process Optimization", level: 88 },
      { name: "Digital Transformation", level: 85 },
      { name: "Innovation Management", level: 83 },
    ],
  },
];

const tools = [
  { name: "Microsoft Excel", icon: PieChart },
  { name: "PowerBI", icon: BarChart3 },
  { name: "Tableau", icon: TrendingUp },
  { name: "Salesforce", icon: Briefcase },
  { name: "SAP", icon: Settings },
  { name: "Slack", icon: MessageSquare },
  { name: "Zoom", icon: Globe },
  { name: "Asana", icon: Target },
];

export const SkillSection = () => {
  return (
    <section id="skills" className="py-20 px-6 bg-muted/30">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            Skills & Expertise
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            A comprehensive skill set developed through years of hands-on
            experience in business consulting, strategic planning, and
            organizational development.
          </p>
        </motion.div>

        {/* Skills Grid */}
        <div className="grid lg:grid-cols-2 gap-8 mb-16">
          {skillCategories.map((category, categoryIndex) => (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: categoryIndex * 0.2 }}
              viewport={{ once: true }}
              className="bg-background/50 backdrop-blur-sm rounded-xl p-8 border border-border/50"
            >
              <div className="flex items-center gap-3 mb-6">
                <div className="p-3 bg-primary/10 rounded-lg">
                  <category.icon className="size-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold text-foreground">
                  {category.title}
                </h3>
              </div>

              <div className="space-y-4">
                {category.skills.map((skill, skillIndex) => (
                  <motion.div
                    key={skill.name}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{
                      duration: 0.6,
                      delay: 0.4 + skillIndex * 0.1,
                    }}
                    viewport={{ once: true }}
                    className="space-y-2"
                  >
                    <div className="flex justify-between items-center">
                      <span className="text-foreground font-medium">
                        {skill.name}
                      </span>
                      <span className="text-sm text-muted-foreground">
                        {skill.level}%
                      </span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <motion.div
                        initial={{ width: 0 }}
                        whileInView={{ width: `${skill.level}%` }}
                        transition={{
                          duration: 1,
                          delay: 0.6 + skillIndex * 0.1,
                        }}
                        viewport={{ once: true }}
                        className="bg-primary h-2 rounded-full"
                      />
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Tools & Technologies */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h3 className="text-3xl font-semibold text-foreground mb-4">
            Tools & Technologies
          </h3>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Proficient in industry-leading tools and platforms that enable
            efficient analysis, collaboration, and project delivery.
          </p>
        </motion.div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {tools.map((tool, index) => (
            <motion.div
              key={tool.name}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
              viewport={{ once: true }}
              className="bg-background/30 backdrop-blur-sm rounded-xl p-6 text-center border border-border/50 hover:border-primary/30 transition-colors group"
            >
              <tool.icon className="size-8 text-muted-foreground group-hover:text-primary mx-auto mb-3 transition-colors" />
              <span className="text-sm font-medium text-foreground">
                {tool.name}
              </span>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};
