import { motion } from "motion/react";
import { Building, Calendar, MapPin } from "lucide-react";

const experiences = [
  {
    title: "Senior Business Consultant",
    company: "Strategic Solutions Inc.",
    location: "New York, NY",
    period: "2020 - Present",
    description:
      "Leading strategic initiatives for Fortune 500 clients, focusing on digital transformation and operational efficiency. Managed a team of 8 consultants and delivered $50M+ in cost savings.",
    achievements: [
      "Increased client operational efficiency by 40% on average",
      "Led digital transformation projects worth $25M+",
      "Developed proprietary frameworks adopted company-wide",
      "Mentored 15+ junior consultants",
    ],
  },
  {
    title: "Business Strategy Manager",
    company: "Global Consulting Group",
    location: "Chicago, IL",
    period: "2017 - 2020",
    description:
      "Specialized in market analysis and strategic planning for mid-market companies. Developed comprehensive business strategies that resulted in significant revenue growth for clients.",
    achievements: [
      "Delivered 25+ strategic planning projects",
      "Achieved 95% client satisfaction rate",
      "Generated $15M in additional revenue for clients",
      "Established new service line for digital strategy",
    ],
  },
  {
    title: "Management Consultant",
    company: "Business Excellence Partners",
    location: "Boston, MA",
    period: "2014 - 2017",
    description:
      "Provided operational consulting services to startups and small businesses. Focused on process optimization, organizational development, and growth strategy implementation.",
    achievements: [
      "Helped 30+ startups scale operations",
      "Reduced operational costs by 30% on average",
      "Implemented lean methodologies across client base",
      "Developed training programs for client teams",
    ],
  },
];

export const ExperienceSection = () => {
  return (
    <section id="experience" className="py-20 px-6 overflow-x-hidden">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            Professional Experience
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Over a decade of experience helping businesses transform, grow, and
            achieve their strategic objectives across various industries and
            company sizes.
          </p>
        </motion.div>

        {/* Experience Timeline */}
        <div className="relative mt-10">
          {/* Timeline Line */}
          <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-border hidden md:block" />

          <div className="space-y-12">
            {experiences.map((exp, index) => (
              <motion.div
                key={exp.title + exp.company}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="relative"
              >
                {/* Timeline Dot */}
                <div className="absolute left-6 top-6 w-4 h-4 bg-primary rounded-full border-4 border-background hidden md:block" />

                {/* Content Card */}
                <div className="md:ml-20 bg-background/50 backdrop-blur-sm rounded-xl p-8 border border-border/50">
                  <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-6">
                    <div className="flex-1">
                      <h3 className="text-2xl font-semibold text-foreground mb-2">
                        {exp.title}
                      </h3>
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-6 text-muted-foreground mb-4">
                        <div className="flex items-center gap-2">
                          <Building className="size-4" />
                          <span className="font-medium">{exp.company}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="size-4" />
                          <span>{exp.location}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="size-4" />
                          <span>{exp.period}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <p className="text-muted-foreground leading-relaxed mb-6">
                    {exp.description}
                  </p>

                  <div>
                    <h4 className="text-lg font-semibold text-foreground mb-4">
                      Key Achievements
                    </h4>
                    <ul className="grid md:grid-cols-2 gap-3">
                      {exp.achievements.map((achievement, achievementIndex) => (
                        <motion.li
                          key={achievementIndex}
                          initial={{ opacity: 0, x: -20 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          transition={{
                            duration: 0.6,
                            delay: 0.4 + achievementIndex * 0.1,
                          }}
                          viewport={{ once: true }}
                          className="flex items-start gap-3 text-muted-foreground"
                        >
                          <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                          <span>{achievement}</span>
                        </motion.li>
                      ))}
                    </ul>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
      <div className="max-w-6xl mx-auto mt-10">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            Teaching and Mentoring Experience
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Over a decade of experience helping businesses transform, grow, and
            achieve their strategic objectives across various industries and
            company sizes.
          </p>
        </motion.div>

        {/* Experience Timeline */}
        <div className="relative">
          {/* Timeline Line */}
          <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-border hidden md:block" />

          <div className="space-y-12">
            {experiences.map((exp, index) => (
              <motion.div
                key={exp.title + exp.company}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="relative"
              >
                {/* Timeline Dot */}
                <div className="absolute left-6 top-6 w-4 h-4 bg-primary rounded-full border-4 border-background hidden md:block" />

                {/* Content Card */}
                <div className="md:ml-20 bg-background/50 backdrop-blur-sm rounded-xl p-8 border border-border/50">
                  <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-6">
                    <div className="flex-1">
                      <h3 className="text-2xl font-semibold text-foreground mb-2">
                        {exp.title}
                      </h3>
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-6 text-muted-foreground mb-4">
                        <div className="flex items-center gap-2">
                          <Building className="size-4" />
                          <span className="font-medium">{exp.company}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="size-4" />
                          <span>{exp.location}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="size-4" />
                          <span>{exp.period}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <p className="text-muted-foreground leading-relaxed mb-6">
                    {exp.description}
                  </p>

                  <div>
                    <h4 className="text-lg font-semibold text-foreground mb-4">
                      Key Achievements
                    </h4>
                    <ul className="grid md:grid-cols-2 gap-3">
                      {exp.achievements.map((achievement, achievementIndex) => (
                        <motion.li
                          key={achievementIndex}
                          initial={{ opacity: 0, x: -20 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          transition={{
                            duration: 0.6,
                            delay: 0.4 + achievementIndex * 0.1,
                          }}
                          viewport={{ once: true }}
                          className="flex items-start gap-3 text-muted-foreground"
                        >
                          <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                          <span>{achievement}</span>
                        </motion.li>
                      ))}
                    </ul>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
