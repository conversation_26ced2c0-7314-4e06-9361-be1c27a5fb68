import { motion } from "motion/react";
import { Award, Trophy, Star, Medal, Crown, Calendar } from "lucide-react";

const awards = [
  {
    title: "Business Excellence Award",
    organization: "National Business Association",
    year: "2023",
    category: "Outstanding Consulting Services",
    description:
      "Recognized for exceptional client satisfaction and innovative business transformation strategies that delivered measurable results across multiple industries.",
    icon: Trophy,
    color: "text-yellow-500",
    bgColor: "bg-yellow-500/10",
  },
  {
    title: "Top 40 Under 40 Business Leaders",
    organization: "Business Weekly Magazine",
    year: "2022",
    category: "Leadership & Innovation",
    description:
      "Selected among the most influential young business leaders for driving organizational change and mentoring the next generation of consultants.",
    icon: Crown,
    color: "text-purple-500",
    bgColor: "bg-purple-500/10",
  },
  {
    title: "Client Choice Award",
    organization: "Consulting Excellence Institute",
    year: "2022",
    category: "Customer Satisfaction",
    description:
      "Awarded based on client testimonials and satisfaction scores, recognizing consistent delivery of high-quality consulting services.",
    icon: Star,
    color: "text-blue-500",
    bgColor: "bg-blue-500/10",
  },
  {
    title: "Innovation in Business Strategy",
    organization: "Strategic Management Society",
    year: "2021",
    category: "Methodology Development",
    description:
      "Honored for developing proprietary frameworks that revolutionized how mid-market companies approach digital transformation.",
    icon: Medal,
    color: "text-green-500",
    bgColor: "bg-green-500/10",
  },
  {
    title: "Outstanding Alumni Achievement",
    organization: "Harvard Business School",
    year: "2020",
    category: "Professional Excellence",
    description:
      "Recognized for significant contributions to the business consulting field and positive impact on the alumni community.",
    icon: Award,
    color: "text-red-500",
    bgColor: "bg-red-500/10",
  },
  {
    title: "Rising Star in Consulting",
    organization: "Consulting Magazine",
    year: "2019",
    category: "Emerging Talent",
    description:
      "Featured as one of the most promising young consultants making significant impact in business transformation and strategic planning.",
    icon: Star,
    color: "text-indigo-500",
    bgColor: "bg-indigo-500/10",
  },
];

export const AwardSection = () => {
  return (
    <section id="awards" className="py-20 px-6 overflow-x-hidden">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            Awards & Recognition
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Recognition for excellence in business consulting, leadership, and
            innovation. These awards reflect my commitment to delivering
            exceptional results for clients.
          </p>
        </motion.div>

        {/* Awards Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {awards.map((award, index) => (
            <motion.div
              key={award.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-background/50 backdrop-blur-sm rounded-xl p-6 border border-border/50 hover:border-primary/30 transition-all duration-300 group"
            >
              {/* Award Icon and Year */}
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 rounded-lg ${award.bgColor}`}>
                  <award.icon className={`size-6 ${award.color}`} />
                </div>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Calendar className="size-4" />
                  <span>{award.year}</span>
                </div>
              </div>

              {/* Award Title */}
              <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-primary transition-colors">
                {award.title}
              </h3>

              {/* Organization and Category */}
              <div className="space-y-1 mb-3">
                <p className="text-sm font-medium text-foreground">
                  {award.organization}
                </p>
                <p className="text-xs text-muted-foreground">
                  {award.category}
                </p>
              </div>

              {/* Description */}
              <p className="text-sm text-muted-foreground leading-relaxed">
                {award.description}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};
