import { Award, Trophy, Star, Medal, Crown } from "lucide-react";

import { Header } from "../header";
import { AwardCard } from "../features/award/components/award-card";

const awards = [
  {
    title: "Business Excellence Award",
    organization: "National Business Association",
    year: "2023",
    category: "Outstanding Consulting Services",
    description:
      "Recognized for exceptional client satisfaction and innovative business transformation strategies that delivered measurable results across multiple industries.",
    icon: Trophy,
    color: "text-yellow-500",
    bgColor: "bg-yellow-500/10",
  },
  {
    title: "Top 40 Under 40 Business Leaders",
    organization: "Business Weekly Magazine",
    year: "2022",
    category: "Leadership & Innovation",
    description:
      "Selected among the most influential young business leaders for driving organizational change and mentoring the next generation of consultants.",
    icon: Crown,
    color: "text-purple-500",
    bgColor: "bg-purple-500/10",
  },
  {
    title: "Client Choice Award",
    organization: "Consulting Excellence Institute",
    year: "2022",
    category: "Customer Satisfaction",
    description:
      "Awarded based on client testimonials and satisfaction scores, recognizing consistent delivery of high-quality consulting services.",
    icon: Star,
    color: "text-blue-500",
    bgColor: "bg-blue-500/10",
  },
  {
    title: "Innovation in Business Strategy",
    organization: "Strategic Management Society",
    year: "2021",
    category: "Methodology Development",
    description:
      "Honored for developing proprietary frameworks that revolutionized how mid-market companies approach digital transformation.",
    icon: Medal,
    color: "text-green-500",
    bgColor: "bg-green-500/10",
  },
  {
    title: "Outstanding Alumni Achievement",
    organization: "Harvard Business School",
    year: "2020",
    category: "Professional Excellence",
    description:
      "Recognized for significant contributions to the business consulting field and positive impact on the alumni community.",
    icon: Award,
    color: "text-red-500",
    bgColor: "bg-red-500/10",
  },
  {
    title: "Rising Star in Consulting",
    organization: "Consulting Magazine",
    year: "2019",
    category: "Emerging Talent",
    description:
      "Featured as one of the most promising young consultants making significant impact in business transformation and strategic planning.",
    icon: Star,
    color: "text-indigo-500",
    bgColor: "bg-indigo-500/10",
  },
];

export const AwardSection = () => {
  return (
    <section id="awards" className="py-20 px-6 overflow-x-hidden">
      <div className="max-w-6xl mx-auto">
        <Header
          title="Awards & Recognition"
          description="Recognition for outstanding contributions to the business consulting field and positive impact on clients and organizations."
        />

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {awards.map((award, index) => (
            <AwardCard key={award.title} award={award} index={index} />
          ))}
        </div>
      </div>
    </section>
  );
};
