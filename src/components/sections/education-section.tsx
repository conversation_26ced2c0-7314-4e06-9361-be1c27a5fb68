import { motion } from "motion/react";
import { GraduationCap, Calendar, MapPin, Award } from "lucide-react";

const education = [
  {
    degree: "Master of Business Administration (MBA)",
    school: "Harvard Business School",
    location: "Boston, MA",
    period: "2012 - 2014",
    description:
      "Specialized in Strategic Management and Organizational Behavior. Graduated Magna Cum Laude with a focus on business transformation and leadership development.",
    achievements: [
      "Dean's List for 4 consecutive semesters",
      "President of Business Strategy Club",
      "Case Competition Winner - McKinsey & Company",
      "Thesis: 'Digital Transformation in Traditional Industries'",
    ],
  },
  {
    degree: "Bachelor of Science in Business Administration",
    school: "University of Pennsylvania - Wharton",
    location: "Philadelphia, PA",
    period: "2008 - 2012",
    description:
      "Concentrated in Finance and Management with a minor in Economics. Developed strong analytical and quantitative skills essential for business consulting.",
    achievements: [
      "Summa Cum Laude graduate (GPA: 3.9/4.0)",
      "Phi Beta Kappa Honor Society",
      "Finance Society Vice President",
      "Undergraduate Research in Behavioral Economics",
    ],
  },
];

const certifications = [
  {
    title: "Certified Management Consultant (CMC)",
    issuer: "Institute of Management Consultants",
    year: "2018",
    description:
      "Professional certification demonstrating expertise in management consulting practices and ethics.",
  },
  {
    title: "Project Management Professional (PMP)",
    issuer: "Project Management Institute",
    year: "2017",
    description:
      "Globally recognized certification for project management excellence and leadership.",
  },
  {
    title: "Six Sigma Black Belt",
    issuer: "American Society for Quality",
    year: "2016",
    description:
      "Advanced certification in process improvement and quality management methodologies.",
  },
  {
    title: "Certified Business Analysis Professional (CBAP)",
    issuer: "International Institute of Business Analysis",
    year: "2015",
    description:
      "Professional certification in business analysis techniques and best practices.",
  },
];

export const EducationSection = () => {
  return (
    <section id="education" className="py-20 px-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            Education & Certifications
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            A strong educational foundation combined with continuous
            professional development to stay at the forefront of business
            consulting practices.
          </p>
        </motion.div>

        {/* Education */}
        <div className="mb-20">
          <motion.h3
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-3xl font-semibold text-foreground mb-12 text-center"
          >
            Academic Background
          </motion.h3>

          <div className="space-y-8">
            {education.map((edu, index) => (
              <motion.div
                key={edu.degree}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="bg-background/50 backdrop-blur-sm rounded-xl p-8 border border-border/50"
              >
                <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-6">
                  <div className="flex-1">
                    <h4 className="text-2xl font-semibold text-foreground mb-2">
                      {edu.degree}
                    </h4>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-6 text-muted-foreground mb-4">
                      <div className="flex items-center gap-2">
                        <GraduationCap className="size-4" />
                        <span className="font-medium">{edu.school}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="size-4" />
                        <span>{edu.location}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="size-4" />
                        <span>{edu.period}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <p className="text-muted-foreground leading-relaxed mb-6">
                  {edu.description}
                </p>

                <div>
                  <h5 className="text-lg font-semibold text-foreground mb-4">
                    Key Achievements
                  </h5>
                  <ul className="grid md:grid-cols-2 gap-3">
                    {edu.achievements.map((achievement, achievementIndex) => (
                      <motion.li
                        key={achievementIndex}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{
                          duration: 0.6,
                          delay: 0.4 + achievementIndex * 0.1,
                        }}
                        viewport={{ once: true }}
                        className="flex items-start gap-3 text-muted-foreground"
                      >
                        <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                        <span>{achievement}</span>
                      </motion.li>
                    ))}
                  </ul>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Certifications */}
        <div>
          <motion.h3
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-3xl font-semibold text-foreground mb-12 text-center"
          >
            Professional Certifications
          </motion.h3>

          <div className="grid md:grid-cols-2 gap-6">
            {certifications.map((cert, index) => (
              <motion.div
                key={cert.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-background/30 backdrop-blur-sm rounded-xl p-6 border border-border/50"
              >
                <div className="flex items-start gap-4 mb-4">
                  <div className="p-3 bg-primary/10 rounded-lg">
                    <Award className="size-6 text-primary" />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-lg font-semibold text-foreground mb-1">
                      {cert.title}
                    </h4>
                    <div className="text-sm text-muted-foreground mb-2">
                      {cert.issuer} • {cert.year}
                    </div>
                  </div>
                </div>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  {cert.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
