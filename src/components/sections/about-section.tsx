import { motion } from "motion/react";
import { Target, Users, TrendingUp, Award } from "lucide-react";

const stats = [
  { icon: Users, label: "Clients Served", value: "150+" },
  { icon: TrendingUp, label: "Revenue Growth", value: "300%" },
  { icon: Target, label: "Success Rate", value: "95%" },
  { icon: Award, label: "Years Experience", value: "10+" },
];

const values = [
  {
    title: "Strategic Thinking",
    description:
      "I approach every challenge with a strategic mindset, analyzing market trends and business dynamics to create sustainable solutions.",
  },
  {
    title: "Client-Centric Approach",
    description:
      "Your success is my priority. I work closely with clients to understand their unique needs and deliver tailored solutions.",
  },
  {
    title: "Innovation & Growth",
    description:
      "I help businesses embrace innovation and identify new opportunities for sustainable growth and competitive advantage.",
  },
  {
    title: "Results-Driven",
    description:
      "Every strategy and recommendation is backed by data and focused on delivering measurable results for your business.",
  },
];

export const AboutSection = () => {
  return (
    <section id="about" className="py-20 px-6 bg-muted/30">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            About Me
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            I'm a passionate business consultant dedicated to helping
            organizations achieve their full potential through strategic
            planning, operational excellence, and innovative solutions.
          </p>
        </motion.div>

        {/* Main Content */}
        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Left Column - Story */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <h3 className="text-2xl font-semibold text-foreground">
              My Journey
            </h3>
            <div className="space-y-4 text-muted-foreground leading-relaxed">
              <p>
                With over a decade of experience in business consulting, I've
                had the privilege of working with companies ranging from
                startups to Fortune 500 enterprises. My journey began in
                corporate strategy, where I developed a deep understanding of
                business operations and market dynamics.
              </p>
              <p>
                Throughout my career, I've specialized in helping businesses
                navigate complex challenges, optimize their operations, and
                identify new growth opportunities. My approach combines
                analytical rigor with creative problem-solving to deliver
                sustainable results.
              </p>
              <p>
                I believe that every business has untapped potential, and my
                mission is to help unlock that potential through strategic
                guidance, operational improvements, and innovative thinking.
              </p>
            </div>
          </motion.div>

          {/* Right Column - Stats */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="grid grid-cols-2 gap-6"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                viewport={{ once: true }}
                className="bg-background/50 backdrop-blur-sm rounded-xl p-6 text-center border border-border/50"
              >
                <stat.icon className="size-8 text-primary mx-auto mb-3" />
                <div className="text-2xl font-bold text-foreground mb-1">
                  {stat.value}
                </div>
                <div className="text-sm text-muted-foreground">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Values Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h3 className="text-3xl font-semibold text-foreground mb-4">
            My Approach
          </h3>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            I believe in building lasting partnerships with my clients, focusing
            on sustainable growth and measurable results.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-8">
          {values.map((value, index) => (
            <motion.div
              key={value.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
              viewport={{ once: true }}
              className="bg-background/30 backdrop-blur-sm rounded-xl p-6 border border-border/50"
            >
              <h4 className="text-xl font-semibold text-foreground mb-3">
                {value.title}
              </h4>
              <p className="text-muted-foreground leading-relaxed">
                {value.description}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};
