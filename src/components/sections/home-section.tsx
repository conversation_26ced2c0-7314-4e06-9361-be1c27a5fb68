import { motion } from "motion/react";

import { Greeting } from "../features/home/<USER>/greeting";
import { NameAndTitle } from "../features/home/<USER>/name-and-title";
import { Description } from "../features/home/<USER>/description";
import { ContactInfo } from "../features/home/<USER>/contact-into";
import { CTA } from "../features/home/<USER>/cta";

export const HomeSection = () => {
  return (
    <section
      id="home"
      className="min-h-screen flex items-center justify-center px-6 relative overflow-x-hidden"
    >
      <div className="max-w-6xl mx-auto text-center">
        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="space-y-8"
        >
          {/* Greeting */}
          <Greeting />

          {/* Name and Title */}
          <NameAndTitle />

          {/* Description */}
          <Description />

          {/* Contact Info */}
          <ContactInfo />

          {/* CTA Buttons */}
          <CTA />
        </motion.div>
      </div>
    </section>
  );
};
