import { motion } from "motion/react";
import { Mail, MapPin } from "lucide-react";
import { TypeAnimation } from "react-type-animation";

import { Button } from "@/components/ui/button";
import { handleClickToSection } from "@/lib/utils";
import { NeonBadge } from "../neon-badge";
import { useTheme } from "@/contexts/theme-context";
import { TextGradient } from "../text-gradient";

export const HomeSection = () => {
  const { actualTheme } = useTheme();
  return (
    <section
      id="home"
      className="min-h-screen flex items-center justify-center px-6 relative overflow-x-hidden"
    >
      <div className="max-w-6xl mx-auto text-center">
        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="space-y-8"
        >
          {/* Greeting */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-lg text-muted-foreground font-medium"
          >
            <NeonBadge color={actualTheme === "dark" ? "purple" : "pink"}>
              Welcome to my portfolio!
            </NeonBadge>
          </motion.div>

          {/* Name and Title */}
          <div className="space-y-4">
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="text-5xl md:text-7xl lg:text-8xl font-bold text-foreground"
            >
              <TextGradient>I'm Hazel</TextGradient>
            </motion.h1>

            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="text-2xl md:text-3xl lg:text-4xl font-semibold text-muted-foreground"
            >
              <TypeAnimation
                preRenderFirstString={true}
                sequence={[
                  500,
                  "I'm Business Consultant",
                  1000,
                  "I'm Strategic Advisor",
                ]}
                speed={50}
                repeat={Infinity}
              />
            </motion.h2>
          </div>

          {/* Description */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.0 }}
            className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed"
          >
            Helping businesses transform and grow through strategic planning,
            operational excellence, and innovative solutions. With over 10 years
            of experience in business consulting.
          </motion.p>

          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.2 }}
            className="flex flex-wrap justify-center gap-6 text-sm text-muted-foreground"
          >
            <div className="flex items-center gap-2">
              <Mail className="size-4" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center gap-2">
              <MapPin className="size-4" />
              <span>Bangkok,Thailand</span>
            </div>
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.4 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Button
              size="lg"
              onClick={() => handleClickToSection("contact")}
              className="px-8 py-3 text-base font-medium w-full xl:w-52 cursor-pointer"
            >
              Get In Touch
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() => handleClickToSection("about")}
              className="px-8 py-3 text-base font-medium  w-full xl:w-52 cursor-pointer"
            >
              Download Resume
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};
