import { <PERSON>, <PERSON>, <PERSON> } from "lucide-react";

import { Button } from "@/components/ui/button";
import { useTheme } from "@/contexts/theme-context";

export function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  const handleThemeChange = () => {
    if (theme === "light") {
      setTheme("dark");
    } else if (theme === "dark") {
      setTheme("system");
    } else {
      setTheme("light");
    }
  };

  const getIcon = () => {
    switch (theme) {
      case "light":
        return <Sun className="size-5" />;
      case "dark":
        return <Moon className="size-5" />;
      case "system":
        return <Monitor className="size-5" />;
      default:
        return <Sun className="size-5" />;
    }
  };

  const getLabel = () => {
    switch (theme) {
      case "light":
        return "Light mode";
      case "dark":
        return "Dark mode";
      case "system":
        return "System mode";
      default:
        return "Light mode";
    }
  };

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={handleThemeChange}
      title={getLabel()}
      className="relative cursor-pointer"
    >
      {getIcon()}
      <span className="sr-only">{getLabel()}</span>
    </Button>
  );
}
