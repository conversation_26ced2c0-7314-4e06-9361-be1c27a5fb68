import { useEffect, useState } from "react";
import { Button } from "./ui/button";
import { CircleArrowUp } from "lucide-react";
import { motion } from "motion/react";

export const ScrollButton = () => {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleScrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  if (!isScrolled) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      transition={{
        duration: 0.4,
        delay: 0.3,
        type: "spring",
        stiffness: 260,
        damping: 20,
      }}
      className="fixed bottom-28 right-6 z-50"
    >
      <Button
        variant="outline"
        size="icon"
        onClick={handleScrollToTop}
        className="relative cursor-pointer bg-background/80 backdrop-blur-lg border border-primary/30!"
      >
        <CircleArrowUp className="size-6" />
      </Button>
    </motion.div>
  );
};
