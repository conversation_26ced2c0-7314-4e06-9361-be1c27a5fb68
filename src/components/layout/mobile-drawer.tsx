import {
  X,
  Home,
  User,
  FolderOpen,
  Mail,
  ChevronRight,
  GraduationCap,
  ListTodo,
  Award,
} from "lucide-react";
import { motion, AnimatePresence } from "motion/react";

import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { ThemeToggle } from "../theme-toggle";
import { menuItems } from "./desktop-nav-bar";
import { handleClickToSection } from "@/lib/utils";
import { useAppStore } from "@/store/use-app-store";
import { Footer } from "./footer";

interface MobileDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const iconMap = {
  Home: Home,
  About: User,
  Experience: FolderOpen,
  Contact: Mail,
  Skills: ListTodo,
  Awards: Award,
  Education: GraduationCap,
};

export const MobileDrawer = ({ open, onOpenChange }: MobileDrawerProps) => {
  const { activeSection } = useAppStore();

  const handleMenuClick = (href: string) => {
    handleClickToSection(href);
    onOpenChange(false);
  };

  return (
    <Drawer open={open} onOpenChange={onOpenChange} direction="right">
      <DrawerContent className="bg-background/95 backdrop-blur-xl border-l border-border/50 shadow-2xl">
        <DrawerHeader className="relative p-0">
          <DrawerTitle className="sr-only">Navigation Menu</DrawerTitle>
          <DrawerDescription className="sr-only">
            Navigate through different sections of the portfolio
          </DrawerDescription>

          <div className="flex items-center justify-between px-6 py-3 border-b border-border/50">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="flex items-center gap-3"
            >
              <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                <span className="text-primary font-bold text-lg">H</span>
              </div>
              <div>
                <h2 className="text-lg font-semibold text-foreground">Hazel</h2>
                <p className="text-sm text-muted-foreground">Portfolio</p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <DrawerClose asChild>
                <Button
                  autoFocus
                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 rounded-full hover:bg-muted/50 transition-colors"
                >
                  <X className="size-5" />
                </Button>
              </DrawerClose>
            </motion.div>
          </div>

          <div className="px-6 py-3 space-y-2">
            <AnimatePresence>
              {menuItems.map((item, index) => {
                const Icon = iconMap[item.name as keyof typeof iconMap];
                const isActive = activeSection === item.href;

                return (
                  <motion.button
                    key={item.name}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 + index * 0.05 }}
                    onClick={() => handleMenuClick(item.href)}
                    className={`
                      group w-full flex items-center justify-between px-4 py-2 rounded-xl
                      transition-all duration-200 hover:scale-[0.98] active:scale-95
                      ${
                        isActive
                          ? "bg-primary/10 border border-primary/20 text-primary"
                          : "hover:bg-muted/50 text-foreground"
                      }
                    `}
                  >
                    <div className="flex items-center gap-4">
                      <div
                        className={`
                        p-2 rounded-lg transition-colors duration-200
                        ${
                          isActive
                            ? "bg-primary/20 text-primary"
                            : "bg-muted/50 text-muted-foreground group-hover:text-foreground"
                        }
                      `}
                      >
                        <Icon className="size-5" />
                      </div>
                      <span className="font-medium text-base">{item.name}</span>
                    </div>

                    <ChevronRight
                      className={`
                      size-4 transition-all duration-200
                      ${
                        isActive
                          ? "text-primary translate-x-1"
                          : "text-muted-foreground group-hover:text-foreground group-hover:translate-x-1"
                      }
                    `}
                    />
                  </motion.button>
                );
              })}
            </AnimatePresence>
          </div>

          <div className="px-6 py-3 border-t border-border/50">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
              className="flex items-center justify-between"
            >
              <div>
                <p className="text-sm font-medium text-foreground">Theme</p>
                <p className="text-xs text-muted-foreground">
                  Switch appearance
                </p>
              </div>
              <ThemeToggle />
            </motion.div>
          </div>

          <div className="p-6 border-t border-border/50 mt-auto">
            <Footer />
          </div>
        </DrawerHeader>
      </DrawerContent>
    </Drawer>
  );
};
