import { X } from "lucide-react";

import {
  <PERSON>er,
  <PERSON>er<PERSON><PERSON>,
  <PERSON>er<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>er<PERSON>it<PERSON>,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { Footer } from "./footer";
import { menuItems } from "./desktop-nav-bar";
import { handleClickToSection } from "@/lib/utils";

interface MobileDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const MobileDrawer = ({ open, onOpenChange }: MobileDrawerProps) => {
  return (
    <Drawer open={open} onOpenChange={onOpenChange} direction="right">
      <DrawerContent className="bg-background/80 backdrop-blur-md">
        <DrawerHeader>
          <DrawerTitle className="text-center">Menu</DrawerTitle>
          <DrawerDescription className="sr-only">
            This action cannot be undone.
          </DrawerDescription>
          <div className="absolute top-10 right-4">
            <DrawerClose asChild>
              <Button variant="outline" size="icon">
                <X className="size-5" />
              </Button>
            </DrawerClose>
          </div>
          <div className="">
            {menuItems.map((item) => (
              <button
                key={item.name}
                onClick={() => handleClickToSection(item.href)}
                className="w-full text-left px-4 py-2 text-sm font-medium transition-all cursor-pointer duration-200 hover:bg-accent hover:text-accent-foreground"
              >
                {item.name}
              </button>
            ))}
          </div>
        </DrawerHeader>
        <DrawerFooter>
          <Footer />
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
};
