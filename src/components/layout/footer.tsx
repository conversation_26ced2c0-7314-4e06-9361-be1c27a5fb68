import { motion } from "motion/react";

export const Footer = () => {
  const year = new Date().getFullYear();
  return (
    <motion.footer
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.5 }}
      className="h-12 w-full flex justify-center items-center"
    >
      <p className="text-sm text-muted-foreground">
        &copy; {year} Hazel. All rights reserved.
      </p>
    </motion.footer>
  );
};
