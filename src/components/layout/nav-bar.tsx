import { ThemeToggle } from "../theme-toggle";

const menuItems = [
  { name: "Home", href: "#home" },
  { name: "About", href: "#about" },
  { name: "Projects", href: "#projects" },
  { name: "Contact", href: "#contact" },
];

export const NavBar = () => {
  const handleClick = (href: string) => {
    // Scroll to the element with the id of href
    const element = document.getElementById(href);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };
  return (
    <nav className="bg-background h-16 w-full sticky top-0 z-50">
      <div className="max-w-6xl mx-auto flex justify-between items-center h-full">
        <div>Hazel</div>
        <div className="flex gap-4">
          {menuItems.map((item) => (
            <button
              key={item.name}
              onClick={() => handleClick(item.href)}
              className="text-foreground hover:text-primary"
            >
              {item.name}
            </button>
          ))}
          <ThemeToggle />
        </div>
      </div>
    </nav>
  );
};
