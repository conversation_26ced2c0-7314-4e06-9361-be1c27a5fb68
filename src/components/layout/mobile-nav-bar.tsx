import { useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import { Menu, X } from "lucide-react";

import { MobileDrawer } from "./mobile-drawer";
import { Button } from "../ui/button";

export const MobileNavBar = () => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  return (
    <>
      <motion.div
        initial={{ opacity: 0, scale: 0.8, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        transition={{
          duration: 0.4,
          delay: 0.3,
          type: "spring",
          stiffness: 260,
          damping: 20,
        }}
        className="md:hidden fixed bottom-42 right-6 z-50"
      >
        <Button
          variant="outline"
          size="icon"
          onClick={() => setIsDrawerOpen(true)}
          className={`
              bg-background/80 backdrop-blur-sm border border-primary/30!
            transition-all duration-300 hover:scale-110 active:scale-95
            ${isDrawerOpen ? "scale-95 opacity-50" : ""}
          `}
        >
          <AnimatePresence mode="wait">
            <motion.div
              key={isDrawerOpen ? "close" : "menu"}
              initial={{ opacity: 0, rotate: -90 }}
              animate={{ opacity: 1, rotate: 0 }}
              exit={{ opacity: 0, rotate: 90 }}
              transition={{ duration: 0.2 }}
            >
              {isDrawerOpen ? (
                <X className="size-6" />
              ) : (
                <Menu className="size-6" />
              )}
            </motion.div>
          </AnimatePresence>
        </Button>

        {/* Ripple effect on tap */}
        <motion.div
          className="absolute inset-0 rounded-full bg-primary/20"
          initial={{ scale: 0, opacity: 0 }}
          animate={{
            scale: isDrawerOpen ? 1.2 : 0,
            opacity: isDrawerOpen ? 0.3 : 0,
          }}
          transition={{ duration: 0.3 }}
        />
      </motion.div>

      {/* Mobile Drawer */}
      <MobileDrawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen} />
    </>
  );
};
