import { useState } from "react";
import { motion } from "motion/react";
import { Menu } from "lucide-react";

import { MobileDrawer } from "./mobile-drawer";
import { Button } from "../ui/button";

export const MobileNavBar = () => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="xl:hidden flex fixed bottom-48 right-6 z-50"
    >
      <Button
        variant="outline"
        size="icon"
        onClick={() => setIsDrawerOpen(true)}
        className="bg-background/80 backdrop-blur-md"
      >
        <Menu className="size-6" />
      </Button>
      <MobileDrawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen} />
    </motion.div>
  );
};
