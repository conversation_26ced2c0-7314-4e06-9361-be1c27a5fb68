import { useEffect } from "react";
import { motion } from "motion/react";

import { handleClickToSection } from "@/lib/utils";
import { ThemeToggle } from "../theme-toggle";
import { useAppStore } from "@/store/use-app-store";

export const menuItems = [
  { name: "Home", href: "home" },
  { name: "About", href: "about" },
  { name: "Education", href: "education" },
  { name: "Skills", href: "skills" },
  { name: "Experience", href: "experience" },
  { name: "Awards", href: "awards" },
  { name: "Contact", href: "contact" },
];

export const DesktopNavBar = () => {
  const { activeSection, setActiveSection } = useAppStore();

  // Track which section is currently in view
  useEffect(() => {
    const handleScroll = () => {
      const sections = menuItems.map((item) => item.href);
      const scrollPosition = window.scrollY + 100; // Offset for navbar

      for (const sectionId of sections) {
        const element = document.getElementById(sectionId);
        if (element) {
          const { offsetTop, offsetHeight } = element;
          if (
            scrollPosition >= offsetTop &&
            scrollPosition < offsetTop + offsetHeight
          ) {
            setActiveSection(sectionId);
            break;
          }
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll();

    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <motion.nav
      initial={{ opacity: 0, y: -90 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
      className="bg-background/80 overflow-x-hidden backdrop-blur-md border-b border-border h-16 w-full sticky top-0 z-50 transition-all duration-300"
    >
      <div className="max-w-6xl mx-auto flex justify-between items-center h-full xl:px-0 px-6">
        <button
          onClick={() => handleClickToSection("home")}
          className="text-2xl cursor-pointer font-bold hover:text-primary transition-colors duration-200"
        >
          Hazel
        </button>
        <div className="flex items-center gap-8">
          <div className="hidden md:flex gap-6">
            {menuItems.map((item) => {
              const sectionId = item.href;
              const isActive = activeSection === sectionId;

              return (
                <button
                  key={item.name}
                  onClick={() => handleClickToSection(item.href)}
                  className={`
                    relative px-3 py-2 text-sm font-medium transition-all cursor-pointer duration-200
                    ${
                      isActive
                        ? "text-primary"
                        : "text-muted-foreground hover:text-foreground"
                    }
                  `}
                >
                  {item.name}
                  {isActive && (
                    <motion.span
                      layoutId="underline"
                      id="underline"
                      className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary rounded-full"
                    />
                  )}
                </button>
              );
            })}
          </div>
          <ThemeToggle />
        </div>
      </div>
    </motion.nav>
  );
};
