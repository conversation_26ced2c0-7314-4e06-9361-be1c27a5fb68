import { motion } from "motion/react";
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

export const MainContent = () => {
  return (
    <div className="grid lg:grid-cols-3 gap-16 items-center mb-20">
      <motion.div
        initial={{ opacity: 0, x: -30 }}
        whileInView={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.8, delay: 0.2 }}
        viewport={{ once: true }}
        className="flex justify-center lg:justify-start"
      >
        <div className="relative">
          <Avatar className="w-64 h-64 border-4 border-primary/20 shadow-2xl">
            <AvatarImage
              src="https://github.com/shadcn.png"
              alt="Hazel - Business Consultant"
              className="object-cover"
            />
            <AvatarFallback className="text-4xl font-bold bg-primary/10 text-primary">
              H
            </AvatarFallback>
          </Avatar>
          <div className="absolute -top-4 -right-4 w-8 h-8 bg-primary/20 rounded-full animate-pulse" />
          <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-primary/30 rounded-full animate-pulse delay-1000" />
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, x: 30 }}
        whileInView={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
        viewport={{ once: true }}
        className="lg:col-span-2 space-y-6"
      >
        <h3 className="text-2xl font-semibold text-foreground">My Journey</h3>
        <div className="space-y-4 text-muted-foreground leading-relaxed">
          <p>
            With over a decade of experience in business consulting, I've had
            the privilege of working with companies ranging from startups to
            Fortune 500 enterprises. My journey began in corporate strategy,
            where I developed a deep understanding of business operations and
            market dynamics.
          </p>
          <p>
            Throughout my career, I've specialized in helping businesses
            navigate complex challenges, optimize their operations, and identify
            new growth opportunities. My approach combines analytical rigor with
            creative problem-solving to deliver sustainable results.
          </p>
          <p>
            I believe that every business has untapped potential, and my mission
            is to help unlock that potential through strategic guidance,
            operational improvements, and innovative thinking.
          </p>
        </div>
      </motion.div>
    </div>
  );
};
