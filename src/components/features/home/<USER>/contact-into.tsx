import { motion } from "motion/react";
import { Mail, MapPin } from "lucide-react";

export const ContactInfo = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 1.2 }}
      className="flex flex-wrap justify-center gap-6 text-sm text-muted-foreground"
    >
      <div className="flex items-center gap-2">
        <Mail className="size-4" />
        <span><EMAIL></span>
      </div>
      <div className="flex items-center gap-2">
        <MapPin className="size-4" />
        <span>Bangkok,Thailand</span>
      </div>
    </motion.div>
  );
};
