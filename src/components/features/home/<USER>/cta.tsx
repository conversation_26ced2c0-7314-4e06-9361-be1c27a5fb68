import { motion } from "motion/react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { handleClickToSection } from "@/lib/utils";

export const CTA = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 1.4 }}
      className="flex flex-col sm:flex-row gap-4 justify-center items-center"
    >
      <Button
        size="lg"
        onClick={() => handleClickToSection("contact")}
        className="px-8 py-3 text-base font-medium w-full xl:w-52 cursor-pointer border border-foreground/20!"
      >
        Get In Touch
      </Button>
      <Button
        variant="outline"
        size="lg"
        onClick={() => handleClickToSection("about")}
        className="px-8 py-3 text-base font-medium  w-full xl:w-52 cursor-pointer bg-secondary! border border-foreground/20!"
      >
        Download Resume
      </Button>
    </motion.div>
  );
};
