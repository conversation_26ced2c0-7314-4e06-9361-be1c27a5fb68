import { motion } from "motion/react";

import { TypeAnimation } from "react-type-animation";
import { TextGradient } from "@/components/text-gradient";

export const NameAndTitle = () => {
  return (
    <div className="space-y-4">
      <motion.h1
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.6 }}
        className="text-5xl md:text-7xl lg:text-8xl font-bold text-foreground"
      >
        <TextGradient>I'm Hazel</TextGradient>
      </motion.h1>

      <motion.h2
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.8 }}
        className="text-2xl md:text-3xl lg:text-4xl font-semibold text-muted-foreground"
      >
        <TypeAnimation
          preRenderFirstString={true}
          sequence={[
            500,
            "I'm Business Consultant",
            1000,
            "I'm Strategic Advisor",
          ]}
          speed={50}
          repeat={Infinity}
        />
      </motion.h2>
    </div>
  );
};
