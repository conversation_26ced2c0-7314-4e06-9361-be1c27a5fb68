import { motion } from "motion/react";

import { NeonBadge } from "@/components/neon-badge";
import { useTheme } from "@/contexts/theme-context";

export const Greeting = () => {
  const { actualTheme } = useTheme();
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.4 }}
      className="text-lg text-muted-foreground font-medium"
    >
      <NeonBadge color={actualTheme === "dark" ? "purple" : "pink"}>
        Welcome to my portfolio!
      </NeonBadge>
    </motion.div>
  );
};
