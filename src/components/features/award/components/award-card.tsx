import { motion } from "motion/react";
import { Calendar } from "lucide-react";

interface AwardCardProps {
  award: {
    title: string;
    organization: string;
    year: string;
    category: string;
    description: string;
    icon: React.ElementType;
    color: string;
    bgColor: string;
  };
  index: number;
}

export const AwardCard = ({ award, index }: AwardCardProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      className="bg-background/50 backdrop-blur-sm rounded-xl p-6 border border-primary/30 hover:border-primary/50 cursor-pointer transition-all duration-300 group"
    >
      {/* Award Icon and Year */}
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 rounded-lg ${award.bgColor}`}>
          <award.icon className={`size-6 ${award.color}`} />
        </div>
        <div className="flex items-center gap-1 text-sm text-muted-foreground">
          <Calendar className="size-4" />
          <span>{award.year}</span>
        </div>
      </div>

      {/* Award Title */}
      <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-primary transition-colors">
        {award.title}
      </h3>

      {/* Organization and Category */}
      <div className="space-y-1 mb-3">
        <p className="text-sm font-medium text-foreground">
          {award.organization}
        </p>
        <p className="text-xs text-muted-foreground">{award.category}</p>
      </div>

      {/* Description */}
      <p className="text-sm text-muted-foreground leading-relaxed">
        {award.description}
      </p>
    </motion.div>
  );
};
