import { motion } from "motion/react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart3,
  TrendingUp,
  <PERSON>rief<PERSON>,
  <PERSON>tings,
  MessageSquare,
  Globe,
  Target,
} from "lucide-react";

const tools = [
  { name: "Microsoft Excel", icon: <PERSON><PERSON><PERSON> },
  { name: "PowerBI", icon: Bar<PERSON>hart3 },
  { name: "Tableau", icon: TrendingUp },
  { name: "Salesforce", icon: Briefcase },
  { name: "SAP", icon: Settings },
  { name: "Slack", icon: MessageSquare },
  { name: "Zoom", icon: Globe },
  { name: "Asana", icon: Target },
];

export const Tool = () => {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
      {tools.map((tool, index) => (
        <motion.div
          key={tool.name}
          initial={{ opacity: 0, scale: 0.8 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
          viewport={{ once: true }}
          className="bg-background/30 backdrop-blur-sm rounded-xl p-6 text-center border border-primary/30 hover:border-primary/50 cursor-pointer transition-colors group"
        >
          <tool.icon className="size-8 text-muted-foreground group-hover:text-primary mx-auto mb-3 transition-colors" />
          <span className="text-sm font-medium text-foreground">
            {tool.name}
          </span>
        </motion.div>
      ))}
    </div>
  );
};
