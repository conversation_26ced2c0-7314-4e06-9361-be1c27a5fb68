import { SkillCard } from "./skill-card";

import { Target, BarChart3, Users, TrendingUp } from "lucide-react";

const skillCategories = [
  {
    title: "Strategic Planning",
    icon: Target,
    skills: [
      { name: "Business Strategy", level: 95 },
      { name: "Market Analysis", level: 90 },
      { name: "Competitive Intelligence", level: 88 },
      { name: "Strategic Planning", level: 92 },
    ],
  },
  {
    title: "Analytics & Data",
    icon: BarChart3,
    skills: [
      { name: "Financial Analysis", level: 90 },
      { name: "Data Analytics", level: 85 },
      { name: "Performance Metrics", level: 88 },
      { name: "Business Intelligence", level: 82 },
    ],
  },
  {
    title: "Leadership & Management",
    icon: Users,
    skills: [
      { name: "Team Leadership", level: 93 },
      { name: "Project Management", level: 90 },
      { name: "Change Management", level: 87 },
      { name: "Stakeholder Management", level: 89 },
    ],
  },
  {
    title: "Business Development",
    icon: TrendingUp,
    skills: [
      { name: "Growth Strategy", level: 91 },
      { name: "Process Optimization", level: 88 },
      { name: "Digital Transformation", level: 85 },
      { name: "Innovation Management", level: 83 },
    ],
  },
];
export const Skill = () => {
  return (
    <div className="grid lg:grid-cols-2 gap-8 mb-16">
      {skillCategories.map((category, categoryIndex) => (
        <SkillCard
          key={category.title}
          category={category}
          categoryIndex={categoryIndex}
        />
      ))}
    </div>
  );
};
