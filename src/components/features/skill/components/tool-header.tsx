import { motion } from "motion/react";

export const ToolHeader = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.2 }}
      viewport={{ once: true }}
      className="text-center mb-12"
    >
      <h3 className="text-3xl font-semibold text-foreground mb-4">
        Tools & Technologies
      </h3>
      <p className="text-muted-foreground max-w-2xl mx-auto">
        Proficient in industry-leading tools and platforms that enable efficient
        analysis, collaboration, and project delivery.
      </p>
    </motion.div>
  );
};
