import { motion } from "motion/react";

interface SkillCardProps {
  category: {
    title: string;
    icon: React.ElementType;
    skills: { name: string; level: number }[];
  };
  categoryIndex: number;
}

export const SkillCard = ({ category, categoryIndex }: SkillCardProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: categoryIndex * 0.2 }}
      viewport={{ once: true }}
      className="bg-background/50 backdrop-blur-sm rounded-xl p-8 border border-primary/30"
    >
      <div className="flex items-center gap-3 mb-6">
        <div className="p-3 bg-primary/10 rounded-lg">
          <category.icon className="size-6 text-primary" />
        </div>
        <h3 className="text-xl font-semibold text-foreground">
          {category.title}
        </h3>
      </div>

      <div className="space-y-4">
        {category.skills.map((skill, skillIndex) => (
          <motion.div
            key={skill.name}
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{
              duration: 0.6,
              delay: 0.4 + skillIndex * 0.1,
            }}
            viewport={{ once: true }}
            className="space-y-2"
          >
            <div className="flex justify-between items-center">
              <span className="text-foreground font-medium">{skill.name}</span>
              <span className="text-sm text-muted-foreground">
                {skill.level}%
              </span>
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <motion.div
                initial={{ width: 0 }}
                whileInView={{ width: `${skill.level}%` }}
                transition={{
                  duration: 1,
                  delay: 0.6 + skillIndex * 0.1,
                }}
                viewport={{ once: true }}
                className="bg-primary h-2 rounded-full"
              />
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};
