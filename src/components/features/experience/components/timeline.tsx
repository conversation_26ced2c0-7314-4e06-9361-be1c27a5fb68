import { ExperienceCard } from "./experience-card";

const experiences = [
  {
    title: "Senior Business Consultant",
    company: "Strategic Solutions Inc.",
    location: "New York, NY",
    period: "2020 - Present",
    description:
      "Leading strategic initiatives for Fortune 500 clients, focusing on digital transformation and operational efficiency. Managed a team of 8 consultants and delivered $50M+ in cost savings.",
    achievements: [
      "Increased client operational efficiency by 40% on average",
      "Led digital transformation projects worth $25M+",
      "Developed proprietary frameworks adopted company-wide",
      "Mentored 15+ junior consultants",
    ],
  },
  {
    title: "Business Strategy Manager",
    company: "Global Consulting Group",
    location: "Chicago, IL",
    period: "2017 - 2020",
    description:
      "Specialized in market analysis and strategic planning for mid-market companies. Developed comprehensive business strategies that resulted in significant revenue growth for clients.",
    achievements: [
      "Delivered 25+ strategic planning projects",
      "Achieved 95% client satisfaction rate",
      "Generated $15M in additional revenue for clients",
      "Established new service line for digital strategy",
    ],
  },
  {
    title: "Management Consultant",
    company: "Business Excellence Partners",
    location: "Boston, MA",
    period: "2014 - 2017",
    description:
      "Provided operational consulting services to startups and small businesses. Focused on process optimization, organizational development, and growth strategy implementation.",
    achievements: [
      "Helped 30+ startups scale operations",
      "Reduced operational costs by 30% on average",
      "Implemented lean methodologies across client base",
      "Developed training programs for client teams",
    ],
  },
];

export const Timeline = () => {
  return (
    <div className="relative mt-10">
      <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-border hidden md:block" />

      <div className="space-y-12">
        {experiences.map((exp, index) => (
          <ExperienceCard
            key={exp.title + exp.company}
            exp={exp}
            index={index}
          />
        ))}
      </div>
    </div>
  );
};
