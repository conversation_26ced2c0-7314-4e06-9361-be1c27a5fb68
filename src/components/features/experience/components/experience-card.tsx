import { motion } from "motion/react";
import { Building, Calendar, MapPin } from "lucide-react";

interface ExperienceCardProps {
  exp: {
    title: string;
    company: string;
    location: string;
    period: string;
    description: string;
    achievements: string[];
  };
  index: number;
}

export const ExperienceCard = ({ exp, index }: ExperienceCardProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: index * 0.2 }}
      viewport={{ once: true }}
      className="relative"
    >
      {/* Timeline Dot */}
      <div className="absolute left-6 top-6 w-4 h-4 bg-primary rounded-full border-4 border-background hidden md:block" />

      {/* Content Card */}
      <div className="md:ml-20 bg-background/50 backdrop-blur-sm rounded-xl p-8 border border-primary/30">
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-6">
          <div className="flex-1">
            <h3 className="text-2xl font-semibold text-foreground mb-2">
              {exp.title}
            </h3>
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-6 text-muted-foreground mb-4">
              <div className="flex items-center gap-2">
                <Building className="size-4" />
                <span className="font-medium">{exp.company}</span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="size-4" />
                <span>{exp.location}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="size-4" />
                <span>{exp.period}</span>
              </div>
            </div>
          </div>
        </div>

        <p className="text-muted-foreground leading-relaxed mb-6">
          {exp.description}
        </p>

        <div>
          <h4 className="text-lg font-semibold text-foreground mb-4">
            Key Achievements
          </h4>
          <ul className="grid md:grid-cols-2 gap-3">
            {exp.achievements.map((achievement, achievementIndex) => (
              <motion.li
                key={achievementIndex}
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{
                  duration: 0.6,
                  delay: 0.4 + achievementIndex * 0.1,
                }}
                viewport={{ once: true }}
                className="flex items-start gap-3 text-muted-foreground"
              >
                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                <span>{achievement}</span>
              </motion.li>
            ))}
          </ul>
        </div>
      </div>
    </motion.div>
  );
};
