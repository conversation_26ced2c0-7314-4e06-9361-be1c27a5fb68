import { motion } from "motion/react";
import { EducationCard } from "./education-card";

const education = [
  {
    degree: "Master of Business Administration (MBA)",
    school: "Harvard Business School",
    location: "Boston, MA",
    period: "2012 - 2014",
    description:
      "Specialized in Strategic Management and Organizational Behavior. Graduated Magna Cum Laude with a focus on business transformation and leadership development.",
    achievements: [
      "Dean's List for 4 consecutive semesters",
      "President of Business Strategy Club",
      "Case Competition Winner - McKinsey & Company",
      "Thesis: 'Digital Transformation in Traditional Industries'",
    ],
  },
  {
    degree: "Bachelor of Science in Business Administration",
    school: "University of Pennsylvania - Wharton",
    location: "Philadelphia, PA",
    period: "2008 - 2012",
    description:
      "Concentrated in Finance and Management with a minor in Economics. Developed strong analytical and quantitative skills essential for business consulting.",
    achievements: [
      "Summa Cum Laude graduate (GPA: 3.9/4.0)",
      "Phi Beta Kappa Honor Society",
      "Finance Society Vice President",
      "Undergraduate Research in Behavioral Economics",
    ],
  },
];

export const Education = () => {
  return (
    <div className="mb-20">
      <motion.h3
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="text-3xl font-semibold text-foreground mb-12 text-center"
      >
        Academic Background
      </motion.h3>

      <div className="space-y-8">
        {education.map((edu, index) => (
          <EducationCard key={edu.degree} edu={edu} index={index} />
        ))}
      </div>
    </div>
  );
};
