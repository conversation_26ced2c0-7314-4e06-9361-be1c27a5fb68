import { motion } from "motion/react";
import { Award } from "lucide-react";

interface CertificateCardProps {
  cert: {
    title: string;
    issuer: string;
    year: string;
    description: string;
    image?: string;
  };
  index: number;
}

export const CertificateCard = ({ cert, index }: CertificateCardProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      className="bg-background/30 backdrop-blur-sm rounded-xl p-6 border border-primary/30"
    >
      <div className="flex items-start gap-4 mb-4">
        <div className="p-3 bg-primary/10 rounded-lg">
          <Award className="size-6 text-primary" />
        </div>
        <div className="flex-1">
          <h4 className="text-lg font-semibold text-foreground mb-1">
            {cert.title}
          </h4>
          <div className="text-sm text-muted-foreground mb-2">
            {cert.issuer} • {cert.year}
          </div>
        </div>
      </div>
      <p className="text-muted-foreground text-sm leading-relaxed">
        {cert.description}
      </p>
      <button className="mt-4 text-primary underline-offset-1 hover:underline">
        <a
          href="https://github.com/shadcn.png"
          target="_blank"
          rel="noopener noreferrer"
          className="text-sm"
        >
          View Certificate
        </a>
      </button>
    </motion.div>
  );
};
