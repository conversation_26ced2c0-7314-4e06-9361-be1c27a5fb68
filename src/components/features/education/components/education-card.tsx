import { motion } from "motion/react";
import { GraduationCap, Calendar, MapPin } from "lucide-react";

interface EducationCardProps {
  edu: {
    degree: string;
    school: string;
    location: string;
    period: string;
    description: string;
    achievements: string[];
  };
  index: number;
}

export const EducationCard = ({ edu, index }: EducationCardProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: index * 0.2 }}
      viewport={{ once: true }}
      className="bg-background/50 backdrop-blur-sm rounded-xl p-8 border border-primary/30"
    >
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-6">
        <div className="flex-1">
          <h4 className="text-2xl font-semibold text-foreground mb-2">
            {edu.degree}
          </h4>
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-6 text-muted-foreground mb-4">
            <div className="flex items-center gap-2">
              <GraduationCap className="size-4" />
              <span className="font-medium">{edu.school}</span>
            </div>
            <div className="flex items-center gap-2">
              <MapPin className="size-4" />
              <span>{edu.location}</span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="size-4" />
              <span>{edu.period}</span>
            </div>
          </div>
        </div>
      </div>

      <p className="text-muted-foreground leading-relaxed mb-6">
        {edu.description}
      </p>

      <div>
        <h5 className="text-lg font-semibold text-foreground mb-4">
          Key Achievements
        </h5>
        <ul className="grid md:grid-cols-2 gap-3">
          {edu.achievements.map((achievement, achievementIndex) => (
            <motion.li
              key={achievementIndex}
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{
                duration: 0.6,
                delay: 0.4 + achievementIndex * 0.1,
              }}
              viewport={{ once: true }}
              className="flex items-start gap-3 text-muted-foreground"
            >
              <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
              <span>{achievement}</span>
            </motion.li>
          ))}
        </ul>
      </div>
    </motion.div>
  );
};
