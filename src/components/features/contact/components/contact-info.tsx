import { motion } from "motion/react";
import { Mail, Phone, MapPin, Clock } from "lucide-react";

const contactInfo = [
  {
    icon: Mail,
    label: "Email",
    value: "<EMAIL>",
    description: "Send me an email anytime",
  },
  {
    icon: Phone,
    label: "Phone",
    value: "+****************",
    description: "Mon-Fri from 9am to 6pm",
  },
  {
    icon: MapPin,
    label: "Location",
    value: "New York, NY",
    description: "Available for in-person meetings",
  },
  {
    icon: Clock,
    label: "Response Time",
    value: "Within 24 hours",
    description: "I'll get back to you quickly",
  },
];

export const ContactInfo = () => {
  return (
    <motion.div
      initial={{ opacity: 0, x: -30 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.8, delay: 0.2 }}
      viewport={{ once: true }}
      className="space-y-8"
    >
      <div>
        <h3 className="text-2xl font-semibold text-foreground mb-6">
          Get In Touch
        </h3>
        <p className="text-muted-foreground leading-relaxed mb-8">
          I'm always excited to discuss new opportunities and challenges.
          Whether you're looking to optimize operations, develop strategic
          plans, or drive growth, let's start a conversation.
        </p>
      </div>

      <div className="space-y-6">
        {contactInfo.map((info, index) => (
          <motion.div
            key={info.label}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
            viewport={{ once: true }}
            className="flex items-start gap-4"
          >
            <div className="p-3 bg-primary/10 rounded-lg">
              <info.icon className="size-6 text-primary" />
            </div>
            <div>
              <h4 className="font-semibold text-foreground mb-1">
                {info.label}
              </h4>
              <p className="text-foreground font-medium mb-1">{info.value}</p>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};
