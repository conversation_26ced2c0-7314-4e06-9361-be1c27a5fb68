{"name": "hazel-portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.544.0", "motion": "^12.23.16", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.63.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.13", "vaul": "^1.1.2", "zod": "^4.1.11", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.35.0", "@types/node": "^24.5.2", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.2", "eslint": "^9.35.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "tw-animate-css": "^1.3.8", "typescript": "~5.8.3", "typescript-eslint": "^8.43.0", "vite": "^7.1.6"}}